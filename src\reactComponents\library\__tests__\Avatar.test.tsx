import React from 'react';
import { render, screen } from '@testing-library/react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { describe, it, expect } from 'vitest';
import Avatar from '../Avatar';
import type { AvatarProps } from '../Avatar';

// Create a test theme for MUI components
const testTheme = createTheme();

// Helper function to render Avatar with theme provider
const renderAvatar = (props: AvatarProps = {}) => {
  return render(
    <ThemeProvider theme={testTheme}>
      <Avatar {...props} />
    </ThemeProvider>,
  );
};

describe('Avatar Component', () => {
  describe('Rendering', () => {
    it('renders without crashing', () => {
      renderAvatar();
      // MUI Avatar renders a fallback PersonIcon when no name is provided
      expect(screen.getByTestId('PersonIcon')).toBeInTheDocument();
    });

    it('renders with default props', () => {
      const { container } = renderAvatar();
      const avatar = container.querySelector('.MuiAvatar-root');
      expect(avatar).toBeInTheDocument();
      expect(avatar).toHaveTextContent('');
    });

    it('renders with custom name', () => {
      renderAvatar({ name: 'John Doe' });
      expect(screen.getByText('JD')).toBeInTheDocument();
    });
  });

  describe('Initials Generation', () => {
    it('generates correct initials for full name', () => {
      renderAvatar({ name: 'John Doe' });
      expect(screen.getByText('JD')).toBeInTheDocument();
    });

    it('generates correct initials for single name', () => {
      renderAvatar({ name: 'John' });
      expect(screen.getByText('J')).toBeInTheDocument();
    });

    it('generates correct initials for multiple names', () => {
      renderAvatar({ name: 'John Michael Doe' });
      expect(screen.getByText('JD')).toBeInTheDocument(); // First and last name initials
    });

    it('handles names with extra spaces', () => {
      renderAvatar({ name: '  John   Doe  ' });
      expect(screen.getByText('JD')).toBeInTheDocument();
    });

    it('handles empty name', () => {
      const { container } = renderAvatar({ name: '' });
      const avatar = container.querySelector('.MuiAvatar-root');
      expect(avatar).toBeInTheDocument();
      expect(avatar).toHaveTextContent('');
    });

    it('handles undefined name', () => {
      const { container } = renderAvatar({ name: undefined });
      const avatar = container.querySelector('.MuiAvatar-root');
      expect(avatar).toBeInTheDocument();
      expect(avatar).toHaveTextContent('');
    });

    it('handles name with only spaces', () => {
      const { container } = renderAvatar({ name: '   ' });
      const avatar = container.querySelector('.MuiAvatar-root');
      expect(avatar).toBeInTheDocument();
      expect(avatar).toHaveTextContent('');
    });

    it('converts initials to uppercase', () => {
      renderAvatar({ name: 'john doe' });
      expect(screen.getByText('JD')).toBeInTheDocument();
    });
  });

  describe('Size Variants', () => {
    it('applies small size styles', () => {
      renderAvatar({ name: 'John Doe', size: 'sm' });
      expect(screen.getByText('JD')).toBeInTheDocument();
      // Note: We can't easily test computed styles in jsdom, but we can test that it renders
    });

    it('applies medium size styles', () => {
      renderAvatar({ name: 'John Doe', size: 'md' });
      expect(screen.getByText('JD')).toBeInTheDocument();
    });

    it('applies large size styles (default)', () => {
      renderAvatar({ name: 'John Doe', size: 'lg' });
      expect(screen.getByText('JD')).toBeInTheDocument();
    });

    it('uses large size as default when size is not specified', () => {
      renderAvatar({ name: 'John Doe' });
      expect(screen.getByText('JD')).toBeInTheDocument();
    });
  });

  describe('Custom Styling', () => {
    it('accepts custom sx prop', () => {
      const customSx = { backgroundColor: 'red' };
      renderAvatar({ name: 'John Doe', sx: customSx });
      expect(screen.getByText('JD')).toBeInTheDocument();
    });

    it('renders without sx prop', () => {
      renderAvatar({ name: 'John Doe' });
      expect(screen.getByText('JD')).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('handles special characters in names', () => {
      renderAvatar({ name: 'José María' });
      expect(screen.getByText('JM')).toBeInTheDocument();
    });

    it('handles numbers in names', () => {
      renderAvatar({ name: 'John2 Doe3' });
      expect(screen.getByText('JD')).toBeInTheDocument();
    });

    it('handles single character names', () => {
      renderAvatar({ name: 'A B' });
      expect(screen.getByText('AB')).toBeInTheDocument();
    });

    it('handles very long names', () => {
      renderAvatar({ name: 'Verylongfirstname Verylonglastname' });
      expect(screen.getByText('VV')).toBeInTheDocument();
    });
  });

  describe('TypeScript Props', () => {
    it('accepts all valid size values', () => {
      // These should compile without TypeScript errors
      const { container: container1 } = renderAvatar({ size: 'sm' });
      const { container: container2 } = renderAvatar({ size: 'md' });
      const { container: container3 } = renderAvatar({ size: 'lg' });

      expect(container1.querySelector('.MuiAvatar-root')).toBeInTheDocument();
      expect(container2.querySelector('.MuiAvatar-root')).toBeInTheDocument();
      expect(container3.querySelector('.MuiAvatar-root')).toBeInTheDocument();
    });

    it('accepts optional props', () => {
      // These should compile without TypeScript errors
      const { container: container1 } = renderAvatar({});
      const { container: container2 } = renderAvatar({ name: 'John' });
      const { container: container3 } = renderAvatar({ size: 'md' });
      const { container: container4 } = renderAvatar({ sx: {} });

      expect(container1.querySelector('.MuiAvatar-root')).toBeInTheDocument();
      expect(container2.querySelector('.MuiAvatar-root')).toBeInTheDocument();
      expect(container3.querySelector('.MuiAvatar-root')).toBeInTheDocument();
      expect(container4.querySelector('.MuiAvatar-root')).toBeInTheDocument();
    });
  });
});
